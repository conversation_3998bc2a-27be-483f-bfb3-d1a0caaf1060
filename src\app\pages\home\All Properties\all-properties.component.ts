import { Component, OnInit, ChangeDetectorRef, HostListener } from '@angular/core';
import { Router } from '@angular/router';
import { HomeService } from '../services/home.service';
import { TranslationService } from '../../../modules/i18n/translation.service';
import { TranslateService } from '@ngx-translate/core';
import { PropertyTranslationService } from '../../../shared/services/property-translation.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-all-properties',
  templateUrl: './all-properties.component.html',
  styleUrls: ['./all-properties.component.scss']
})
export class AllPropertiesComponent implements OnInit {
  properties: any[] = [];
  isLoading: boolean = false;
  currentPage: number = 0;
  limit: number = 20;
  hasMoreProperties: boolean = true;
  searchText: string = '';
  filteredProperties: any[] = [];
  currentLang: string = 'en';

  appliedFilters: any = {};

  // Header related properties
  currentUser: any = null;
  isLoggedIn: boolean = false;
  showUserDropdown: boolean = false;
  showMobileMenu: boolean = false;

  // Filter variables
  selectedPropertyType: string = '';
  selectedLocation: string = '';
  selectedHouseType: string = '';
  minPrice: number | null = null;
  maxPrice: number | null = null;

  viewMode: 'grid' | 'list' = 'grid';

  // Price Range Slider variables
  priceMin: number = 100000; // 1 million (starting point)
  priceMax: number = 10000000; // 50 million
  priceStep: number = 100000; // 100k steps
  minPriceValue: number = 1000000; // Default min: 1M
  maxPriceValue: number = 10000000; // Default max: 10M
  isDragging: boolean = false;
  dragType: 'min' | 'max' = 'min';

  // Unit Area Range Slider Properties
  areaMin: number = 50; // 50 m² (starting point)
  areaMax: number = 1000; // 1000 m²
  areaStep: number = 10; // 10 m² steps
  minAreaValue: number = 50; // Default min: 50 m²
  maxAreaValue: number = 300; // Default max: 300 m²
  isAreaDragging: boolean = false;
  areaDragType: 'min' | 'max' = 'min';

  // Dropdown management for exact filter
  activeDropdown: string | null = null;
  isAreaDropdownVisible: boolean = false;
  selectedAreaId: string = '';
  areas: any[] = [];
  isUnitTypeDropdownVisible: boolean = false;
  selectedUnitTypeValue: string = '';
  unitTypes: any[] = [];

  constructor(
    private homeService: HomeService,
    private cd: ChangeDetectorRef,
    private router: Router,
    public translationService: TranslationService,
    private translateService: TranslateService,
    private propertyTranslationService: PropertyTranslationService
  ) {}

  ngOnInit(): void {
    this.checkUserSession();
    this.loadProperties();
    this.loadAreas();
    this.loadUnitTypes();

    // Get current language
    this.currentLang = this.translationService.getCurrentLanguage();

    // Subscribe to language changes
    this.translationService.currentLanguage$.subscribe(lang => {
      this.currentLang = lang;
      this.cd.detectChanges();
    });
  }

  // Header related methods
  checkUserSession(): void {
    // Check if user is logged in by checking localStorage
    const authToken = localStorage.getItem('authToken');
    const currentUser = localStorage.getItem('currentUser');

    if (authToken && currentUser) {
      try {
        this.currentUser = JSON.parse(currentUser);
        this.isLoggedIn = true;
      } catch (error) {
        // If parsing fails, user is not logged in
        this.isLoggedIn = false;
        this.currentUser = null;
      }
    } else {
      this.isLoggedIn = false;
      this.currentUser = null;
    }
  }

  getUserDisplayName(): string {
    if (this.currentUser) {
      return this.currentUser.fullName || 'User';
    }
    return 'Guest';
  }

  getUserProfileImage(): string {
    if (this.currentUser && this.currentUser.image) {
      return this.currentUser.image;
    }
    // Return default avatar if no profile image
    return 'assets/media/avatars/blank.png';
  }

  // Get user role
  getUserRole(): string {
    if (this.currentUser && this.currentUser.role) {
      return this.currentUser.role.toLowerCase();
    }
    return '';
  }

  // Check if user is admin
  isAdmin(): boolean {
    return this.getUserRole() === 'admin';
  }

  // Check if user is client
  isClient(): boolean {
    return this.getUserRole() === 'client';
  }

  // Check if user is developer
  isDeveloper(): boolean {
    return this.getUserRole() === 'developer';
  }

  // Check if user is broker
  isBroker(): boolean {
    return this.getUserRole() === 'broker';
  }

  toggleUserDropdown(): void {
    this.showUserDropdown = !this.showUserDropdown;
  }

  closeUserDropdown(): void {
    this.showUserDropdown = false;
  }

  logout(): void {
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');
    this.isLoggedIn = false;
    this.currentUser = null;
    this.showUserDropdown = false;
    // Optionally redirect to login page
    this.router.navigate(['/authentication/login']);
  }

  toggleMobileMenu(): void {
    this.showMobileMenu = !this.showMobileMenu;
    // Close user dropdown when mobile menu is toggled
    if (this.showMobileMenu) {
      this.showUserDropdown = false;
    }
  }

  closeMobileMenu(): void {
    this.showMobileMenu = false;
  }

  // Navigate to requests page
  navigateToRequests(): void {
    this.closeUserDropdown();
    this.router.navigate(['/requests/sent']);
  }

  // Navigate to profile page
  navigateToProfile(): void {
    this.closeUserDropdown();
    window.location.href = '/profile';
  }

  // Navigate to chat/messages page
  navigateToChat(): void {
    this.closeUserDropdown();
    this.router.navigate(['/chat']);
  }

  // Navigate to stepper modal (new request)
  navigateToStepperModal(): void {
    this.closeUserDropdown();
    this.router.navigate(['/broker/stepper-modal']);
  }

  // Navigate to dashboard
  navigateToDashboard(): void {
    this.closeUserDropdown();

    if(this.isAdmin()){
      this.router.navigate(['/super-admin/dashboard']).catch(error => {
        this.router.navigate(['/super-admin']);
      });
    } else if(this.isDeveloper()){
      this.router.navigate(['/developer/dashboards']).catch(error => {
        this.router.navigate(['/developer/projects']).catch(fallbackError => {
          this.router.navigate(['/developer']);
        });
      });
    } else if (this.isBroker()){
      this.router.navigate(['/broker/dashboard']).catch(error => {
        this.router.navigate(['/broker']);
      });
    } else {
      this.router.navigate(['/home']);
    }
  }

  scrollToSection() {
    const element = document.getElementById('properties-section');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    const userProfile = target.closest('.user-profile');
    const userDropdown = target.closest('.user-dropdown');
    const navbarToggler = target.closest('.navbar-toggler');
    const mobileNavDropdown = target.closest('.mobile-nav-dropdown');
    const areaDropdown = target.closest('.area-dropdown-exact');
    const unitTypeDropdown = target.closest('.unit-type-dropdown-exact');

    // Close user dropdown if clicked outside of user profile and dropdown
    if (!userProfile && !userDropdown && this.showUserDropdown) {
      this.showUserDropdown = false;
    }

    // Close mobile menu if clicked outside of navbar toggler and mobile nav dropdown
    if (!navbarToggler && !mobileNavDropdown && this.showMobileMenu) {
      this.showMobileMenu = false;
    }

    // Close area dropdown if clicked outside
    if (!areaDropdown && this.isAreaDropdownVisible) {
      this.isAreaDropdownVisible = false;
    }

    // Close unit type dropdown if clicked outside
    if (!unitTypeDropdown && this.isUnitTypeDropdownVisible) {
      this.isUnitTypeDropdownVisible = false;
    }
  }

  loadProperties(loadMore: boolean = false): void {
    this.isLoading = true;

    const offset = loadMore ? this.currentPage * this.limit : 0;

    this.homeService.getFeaturedProperties(this.limit, offset).subscribe({
      next: (response: any) => {
        console.log('Properties response:', response);

        // Log property types for debugging
        if (response.data && response.data.length > 0) {
          console.log('First property:', response.data[0]);
          console.log('Property type field:', response.data[0].type);
          console.log('Property unitType field:', response.data[0].unitType);
        }

        if (loadMore) {
           this.properties = [...this.properties, ...(response.data || [])];
        } else {
           this.properties = response.data || [];
        }

        // Update filtered properties after loading
        this.applyFilters();

        if (loadMore) {
          this.currentPage++;
        }

        this.isLoading = false;
        this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Error loading properties:', error);
        this.isLoading = false;
        this.cd.detectChanges();
      }
    });
  }

  loadMoreProperties(): void {
    if (!this.isLoading && this.hasMoreProperties) {
      this.loadProperties(true);
    }
  }

  loadAreas(): void {
    // Mock areas data - replace with actual API call
    this.areas = [
      { id: '1', name_ar: 'التجمع الخامس', name_en: '5th Settlement' },
      { id: '2', name_ar: 'القاهرة الجديدة', name_en: 'New Cairo' },
      { id: '3', name_ar: 'المعادي', name_en: 'Maadi' },
      { id: '4', name_ar: 'الزمالك', name_en: 'Zamalek' },
      { id: '5', name_ar: 'مدينة نصر', name_en: 'Nasr City' },
      { id: '6', name_ar: 'الشيخ زايد', name_en: 'Sheikh Zayed' },
      { id: '7', name_ar: '6 أكتوبر', name_en: '6th of October' }
    ];
  }

  loadUnitTypes(): void {
    // Mock unit types data - replace with actual API call
    this.unitTypes = [
      { key: this.translationService.isRTL() ? 'شقة' : 'Apartment', value: 'apartment' },
      { key: this.translationService.isRTL() ? 'فيلا' : 'Villa', value: 'villa' },
      { key: this.translationService.isRTL() ? 'بنتهاوس' : 'Penthouse', value: 'penthouse' },
      { key: this.translationService.isRTL() ? 'استوديو' : 'Studio', value: 'studio' },
      { key: this.translationService.isRTL() ? 'دوبلكس' : 'Duplex', value: 'duplex' },
      { key: this.translationService.isRTL() ? 'تاون هاوس' : 'Townhouse', value: 'townhouse' },
      { key: this.translationService.isRTL() ? 'مكتب' : 'Office', value: 'office' },
      { key: this.translationService.isRTL() ? 'محل تجاري' : 'Shop', value: 'shop' }
    ];
  }

  onPropertyClick(property: any): void {
    const authToken = localStorage.getItem('authToken');
    const currentUserData = localStorage.getItem('currentUser');

    if (!authToken || !currentUserData) {
      Swal.fire({
        title: this.translateService.instant('HOME.ALL_PROPERTIES.LOGIN_REQUIRED'),
        text: this.translateService.instant('HOME.ALL_PROPERTIES.LOGIN_MESSAGE'),
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: this.translateService.instant('HOME.ALL_PROPERTIES.LOGIN_BUTTON'),
        cancelButtonText: this.translateService.instant('HOME.ALL_PROPERTIES.CANCEL_BUTTON'),
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#e74c3c'
      }).then((result) => {
        if (result.isConfirmed) {
          this.router.navigate(['/authentication/login']);
        }
      });
      return;
    }
    try {
      const currentUser = JSON.parse(currentUserData);
      if (!currentUser || !currentUser.role || currentUser.role.trim() === '') {
        Swal.fire({
          title: this.translateService.instant('HOME.ALL_PROPERTIES.LOGIN_REQUIRED'),
          text: this.translateService.instant('HOME.ALL_PROPERTIES.LOGIN_ROLE_MESSAGE'),
          icon: 'warning',
          showCancelButton: true,
          confirmButtonText: this.translateService.instant('HOME.ALL_PROPERTIES.LOGIN_BUTTON'),
          cancelButtonText: this.translateService.instant('HOME.ALL_PROPERTIES.CANCEL_BUTTON'),
          confirmButtonColor: '#28a745',
          cancelButtonColor: '#e74c3c'
        }).then((result) => {
          if (result.isConfirmed) {
            this.router.navigate(['/authentication/login']);
          }
        });
        return;
      }
    } catch (error) {
      // If parsing fails, redirect to login
      Swal.fire({
        title: this.translateService.instant('HOME.ALL_PROPERTIES.LOGIN_REQUIRED'),
        text: this.translateService.instant('HOME.ALL_PROPERTIES.INVALID_SESSION'),
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: this.translateService.instant('HOME.ALL_PROPERTIES.LOGIN_BUTTON'),
        cancelButtonText: this.translateService.instant('HOME.ALL_PROPERTIES.CANCEL_BUTTON'),
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#e74c3c'
      }).then((result) => {
        if (result.isConfirmed) {
          this.router.navigate(['/authentication/login']);
        }
      });
      return;
    }

    // If user is logged in and has role, navigate to property details
    this.router.navigate(['developer/projects/models/units/details'], {
      queryParams: {
        unitId: property.id
      }
    });
  }

  formatPrice(property: any): string {
    const currency = this.translateService.instant('HOME.CURRENCY.EGP');
    const priceOnRequest = this.currentLang === 'ar' ? 'السعر عند الطلب' : 'Price on request';
    const monthText = this.currentLang === 'ar' ? 'شهرياً' : 'month';

    if (property.unitOperation === 'Rent') {
      if (property.monthlyRent) {
        const formattedRent = this.currentLang === 'ar'
          ? this.convertToArabicNumbers(property.monthlyRent.toLocaleString())
          : property.monthlyRent.toLocaleString();
        return `${formattedRent} ${currency}/${monthText}`;
      } else {
        return priceOnRequest;
      }
    } else {
      const price = property.totalPriceInCash || property.totalPriceInInstallment;
      if (price) {
        const formattedPrice = this.currentLang === 'ar'
          ? this.convertToArabicNumbers(price.toLocaleString())
          : price.toLocaleString();
        return `${formattedPrice} ${currency}`;
      } else {
        return priceOnRequest;
      }
    }
  }

  getFormattedPrice(property: any): string {
    return this.formatPrice(property);
  }

  getTranslatedPropertyType(type: string): string {
    console.log('Property type:', type, 'Current lang:', this.currentLang);

    const translation = this.propertyTranslationService.translatePropertyType(
      type,
      this.currentLang as 'en' | 'ar',
      20
    );

    console.log('Normalized type:', type.trim().toLowerCase(), 'Translation:', translation);

    return translation;
  }

  // الحصول على اسم المنطقة حسب اللغة
  getAreaName(property: any): string {
    if (this.currentLang === 'en') {
      return (property.area?.name_en || property.city?.name_en || property.area?.name_ar || property.city?.name_ar || '').slice(0, 20);
    } else {
      return (property.area?.name_ar || property.city?.name_ar || property.area?.name_en || property.city?.name_en || '').slice(0, 20);
    }
  }

  getPropertyType(property: any): string {
    return this.getTranslatedPropertyType(property.type);
  }

  // Reset filters
  resetFilters(): void {
    this.selectedPropertyType = '';
    this.selectedLocation = '';
    this.minPrice = null;
    this.maxPrice = null;

    this.searchText = '';
    this.minPriceValue = 1000000; // 1M
    this.maxPriceValue = 10000000; // 10M
    this.applyFilters();
  }

  // Price Range Slider Functions
  getMinPercentage(): number {
    return ((this.minPriceValue - this.priceMin) / (this.priceMax - this.priceMin)) * 100;
  }

  getMaxPercentage(): number {
    return ((this.maxPriceValue - this.priceMin) / (this.priceMax - this.priceMin)) * 100;
  }

  getRangeWidth(): number {
    return this.getMaxPercentage() - this.getMinPercentage();
  }

  formatPriceValue(value: number): string {
    if (value >= 1000000) {
      return (value / 1000000).toFixed(1) + 'M';
    } else if (value >= 1000) {
      return (value / 1000).toFixed(0) + 'K';
    }
    return value.toString();
  }

  // Format area value for display
  formatAreaValue(value: number): string {
    return value.toString();
  }

  onRangeChange(): void {
    // Ensure min is always less than max
    if (this.minPriceValue >= this.maxPriceValue) {
      this.minPriceValue = this.maxPriceValue - this.priceStep;
    }

    // Update the filter values
    this.minPrice = this.minPriceValue;
    this.maxPrice = this.maxPriceValue;
  }

  // Unit Area Slider Methods
  getAreaMinPercentage(): number {
    return ((this.minAreaValue - this.areaMin) / (this.areaMax - this.areaMin)) * 100;
  }

  getAreaMaxPercentage(): number {
    return ((this.maxAreaValue - this.areaMin) / (this.areaMax - this.areaMin)) * 100;
  }

  getAreaRangeWidth(): number {
    return this.getAreaMaxPercentage() - this.getAreaMinPercentage();
  }

  onAreaRangeChange(): void {
    // Ensure min is always less than max
    if (this.minAreaValue >= this.maxAreaValue) {
      this.minAreaValue = this.maxAreaValue - this.areaStep;
    }
  }

  // Unit Area Drag Methods
  startAreaDrag(event: MouseEvent | TouchEvent, type: 'min' | 'max'): void {
    event.preventDefault();
    event.stopPropagation();
    this.isAreaDragging = true;
    this.areaDragType = type;

    const moveHandler = (e: MouseEvent | TouchEvent) => {
      if (this.isAreaDragging) {
        e.preventDefault();
        this.handleAreaDrag(e);
      }
    };

    const endHandler = () => {
      this.isAreaDragging = false;
      document.removeEventListener('mousemove', moveHandler);
      document.removeEventListener('mouseup', endHandler);
      document.removeEventListener('touchmove', moveHandler);
      document.removeEventListener('touchend', endHandler);
    };

    document.addEventListener('mousemove', moveHandler, { passive: false });
    document.addEventListener('mouseup', endHandler);
    document.addEventListener('touchmove', moveHandler, { passive: false });
    document.addEventListener('touchend', endHandler);
  }

  handleAreaDrag(event: MouseEvent | TouchEvent): void {
    const track = document.querySelector('.area-slider-exact .slider-track') as HTMLElement;
    if (!track) return;

    const rect = track.getBoundingClientRect();
    const clientX = event instanceof MouseEvent ? event.clientX : event.touches[0].clientX;
    const percentage = Math.max(0, Math.min(100, ((clientX - rect.left) / rect.width) * 100));
    const value = this.areaMin + (percentage / 100) * (this.areaMax - this.areaMin);
    const roundedValue = Math.round(value / this.areaStep) * this.areaStep;

    if (this.areaDragType === 'min') {
      this.minAreaValue = Math.max(this.areaMin, Math.min(roundedValue, this.maxAreaValue - this.areaStep));
    } else {
      this.maxAreaValue = Math.min(this.areaMax, Math.max(roundedValue, this.minAreaValue + this.areaStep));
    }
  }

  onAreaTrackClick(event: MouseEvent): void {
    if (this.isAreaDragging) return;

    const track = event.currentTarget as HTMLElement;
    const rect = track.getBoundingClientRect();
    const percentage = ((event.clientX - rect.left) / rect.width) * 100;
    const value = this.areaMin + (percentage / 100) * (this.areaMax - this.areaMin);
    const roundedValue = Math.round(value / this.areaStep) * this.areaStep;

    const minDistance = Math.abs(roundedValue - this.minAreaValue);
    const maxDistance = Math.abs(roundedValue - this.maxAreaValue);

    if (minDistance < maxDistance) {
      this.minAreaValue = Math.max(this.areaMin, Math.min(roundedValue, this.maxAreaValue - this.areaStep));
    } else {
      this.maxAreaValue = Math.min(this.areaMax, Math.max(roundedValue, this.minAreaValue + this.areaStep));
    }
  }

  // Area Dropdown Methods
  toggleAreaDropdown(): void {
    this.isAreaDropdownVisible = !this.isAreaDropdownVisible;
  }

  selectArea(areaId: string): void {
    this.selectedAreaId = areaId;
    this.isAreaDropdownVisible = false;
    // Update filter logic here if needed
  }

  getSelectedAreaText(): string {
    if (!this.selectedAreaId) {
      return this.translationService.isRTL() ? 'جميع المناطق' : 'All Areas';
    }

    const selectedArea = this.areas.find(area => area.id === this.selectedAreaId);
    if (selectedArea) {
      return this.translationService.getCurrentLanguage() === 'ar'
        ? (selectedArea.name_ar || selectedArea.name_en)
        : selectedArea.name_en;
    }

    return this.translationService.isRTL() ? 'جميع المناطق' : 'All Areas';
  }

  // Unit Type Dropdown Methods
  toggleUnitTypeDropdown(): void {
    this.isUnitTypeDropdownVisible = !this.isUnitTypeDropdownVisible;
  }

  selectUnitType(unitTypeValue: string): void {
    this.selectedUnitTypeValue = unitTypeValue;
    this.isUnitTypeDropdownVisible = false;
    // Update filter logic here if needed
  }

  getSelectedUnitTypeText(): string {
    if (!this.selectedUnitTypeValue) {
      return this.translationService.isRTL() ? 'نوع العقار  ' : 'All Types';
    }

    const selectedType = this.unitTypes.find(type => type.value === this.selectedUnitTypeValue);
    if (selectedType) {
      return selectedType.key;
    }

    return this.translationService.isRTL() ? '   نوع العقار' : 'All Types';
  }

  startDrag(event: MouseEvent | TouchEvent, type: 'min' | 'max'): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = true;
    this.dragType = type;

    const moveHandler = (e: MouseEvent | TouchEvent) => {
      if (this.isDragging) {
        e.preventDefault();
        this.handleDrag(e);
      }
    };

    const endHandler = () => {
      this.isDragging = false;
      document.removeEventListener('mousemove', moveHandler);
      document.removeEventListener('mouseup', endHandler);
      document.removeEventListener('touchmove', moveHandler);
      document.removeEventListener('touchend', endHandler);
    };

    document.addEventListener('mousemove', moveHandler, { passive: false });
    document.addEventListener('mouseup', endHandler);
    document.addEventListener('touchmove', moveHandler, { passive: false });
    document.addEventListener('touchend', endHandler);
  }

  private handleDrag(event: MouseEvent | TouchEvent): void {
    const track = document.querySelector('.slider-track') as HTMLElement;
    if (!track) return;

    const rect = track.getBoundingClientRect();
    const clientX = event instanceof MouseEvent ? event.clientX : event.touches[0].clientX;
    const percentage = Math.max(0, Math.min(100, ((clientX - rect.left) / rect.width) * 100));
    const value = this.priceMin + (percentage / 100) * (this.priceMax - this.priceMin);
    const roundedValue = Math.round(value / this.priceStep) * this.priceStep;

    if (this.dragType === 'min') {
      this.minPriceValue = Math.max(this.priceMin, Math.min(roundedValue, this.maxPriceValue - this.priceStep));
    } else {
      this.maxPriceValue = Math.max(this.minPriceValue + this.priceStep, Math.min(roundedValue, this.priceMax));
    }

    this.onRangeChange();
  }

  onTrackClick(event: MouseEvent): void {
    if (this.isDragging) return; // Don't handle clicks while dragging

    const track = event.currentTarget as HTMLElement;
    const rect = track.getBoundingClientRect();
    const percentage = Math.max(0, Math.min(100, ((event.clientX - rect.left) / rect.width) * 100));
    const value = this.priceMin + (percentage / 100) * (this.priceMax - this.priceMin);
    const roundedValue = Math.round(value / this.priceStep) * this.priceStep;

    // Determine which handle is closer
    const minDistance = Math.abs(roundedValue - this.minPriceValue);
    const maxDistance = Math.abs(roundedValue - this.maxPriceValue);

    if (minDistance < maxDistance) {
      this.minPriceValue = Math.max(this.priceMin, Math.min(roundedValue, this.maxPriceValue - this.priceStep));
    } else {
      this.maxPriceValue = Math.max(this.minPriceValue + this.priceStep, Math.min(roundedValue, this.priceMax));
    }

    this.onRangeChange();
  }

  getPropertyLocation(property: any): string {
    if (this.currentLang === 'en') {
      return property.area?.name_en || property.city?.name_en || property.area?.name_ar || property.city?.name_ar || 'Location not specified';
    } else {
      return property.area?.name_ar || property.city?.name_ar || property.area?.name_en || property.city?.name_en || 'الموقع غير محدد';
    }
  }

  getPropertyImage(property: any): string {
    return property.gallery?.[0]?.url || 'assets/media/auth/404-error.png';
  }

  goBack(): void {
    this.router.navigate(['/home']);
  }

  onSearchChange(): void {
    this.applyFilters();
  }



  // ترجمة عمليات العقار
  getTranslatedOperation(operation: string): string {
    const operationMap: { [key: string]: string } = {
      'sell': 'SELL',
      'purchasing': 'PURCHASING',
      'rent in': 'RENT_IN',
      'rent out': 'RENT_OUT'
    };

    const translationKey = operationMap[operation?.toLowerCase()] || 'SELL';
    return this.translateService.instant(`HOME.PROPERTY_OPERATIONS.${translationKey}`);
  }



  // تحويل الأرقام إلى العربية
  convertToArabicNumbers(englishNumbers: string): string {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    const englishNumbersArray = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

    let result = englishNumbers;
    for (let i = 0; i < englishNumbersArray.length; i++) {
      result = result.replace(new RegExp(englishNumbersArray[i], 'g'), arabicNumbers[i]);
    }
    return result;
  }

  // تنسيق المساحة حسب اللغة
  getFormattedArea(area: number): string {
    const unit = this.translateService.instant('HOME.UNITS.SQM');

    if (this.currentLang === 'ar') {
      const arabicArea = this.convertToArabicNumbers(area.toString());
      return `${arabicArea} ${unit}`;
    } else {
      return `${area} ${unit}`;
    }
  }



  // ترجمة نوع الكمبوند
  getTranslatedCompoundType(type: string): string {
    return this.propertyTranslationService.translateCompoundType(
      type,
      this.currentLang as 'en' | 'ar',
      20
    );
  }



  // Apply filters to properties
  applyFilters(): void {
    this.filteredProperties = this.properties.filter(property => {
      // Apply search filter first
      let matchesSearch = true;
      if (this.searchText.trim()) {
        const searchTerm = this.searchText.toLowerCase().trim();
        const type = this.getTranslatedPropertyType(property.unitType).toLowerCase();
        const location = this.getPropertyLocation(property).toLowerCase();
        const compoundType = this.getTranslatedCompoundType(property.compoundType).toLowerCase();
        const operation = this.getTranslatedOperation(property.unitOperation).toLowerCase();
        const bathrooms = (property.numberOfBathrooms || '').toString();

        matchesSearch = type.includes(searchTerm) ||
                       location.includes(searchTerm) ||
                       compoundType.includes(searchTerm) ||
                       operation.includes(searchTerm) ||
                       bathrooms.includes(searchTerm);
      }

      if (!matchesSearch) return false;

      // Apply unit filters
      if (this.appliedFilters.area && property.area?.id != this.appliedFilters.area) {
        return false;
      }

      if (this.appliedFilters.unitType && property.unitType !== this.appliedFilters.unitType) {
        return false;
      }

      if (this.appliedFilters.view && property.view !== this.appliedFilters.view) {
        return false;
      }

      if (this.appliedFilters.unitArea && property.unitArea < parseFloat(this.appliedFilters.unitArea)) {
        return false;
      }

      if (this.appliedFilters.price && property.price > parseFloat(this.appliedFilters.price)) {
        return false;
      }

      return true;
    });

    this.cd.detectChanges();
  }

  // Dropdown management methods for exact filter
  toggleDropdown(type: string): void {
    this.activeDropdown = this.activeDropdown === type ? null : type;
  }

  selectOption(type: string, value: string): void {
    switch (type) {
      case 'rent':
        this.selectedPropertyType = value;
        break;
      case 'house':
        this.selectedHouseType = value;
        break;

    }
    this.activeDropdown = null;
  }
}

